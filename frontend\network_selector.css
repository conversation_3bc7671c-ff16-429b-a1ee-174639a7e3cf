/* 前端路网选择器样式 */

.network-selector-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

.network-selector-modal {
    width: 95%;
    height: 90%;
    max-width: 90vw;
    background-color: #102735;
    border-radius: 0.4vw;
    display: flex;
    flex-direction: column;
    box-shadow: 0 0.2vh 1vh rgba(0, 0, 0, 0.5);
}

.selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8vh 1vw;
    border-bottom: 0.05vw solid rgba(255, 255, 255, 0.1);
    background-color: #05202e;
    border-radius: 0.4vw 0.4vw 0 0;
}

.selector-header h3 {
    margin: 0;
    color: #ffffff;
    font-size: 0.9vw;
}

.close-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.2vw;
    cursor: pointer;
    padding: 0;
    width: 1.5vw;
    height: 1.5vw;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.2vw;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.selector-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8vh 1vw;
    border-bottom: 0.05vw solid rgba(255, 255, 255, 0.1);
    background-color: #102735;
}

.selector-controls .info {
    display: flex;
    gap: 1vw;
    color: #ffffff;
    font-size: 0.7vw;
}

.selector-controls .buttons {
    display: flex;
    gap: 0.5vw;
}

.selector-controls button {
    padding: 0.4vh 0.8vw;
    border: none;
    border-radius: 0.2vw;
    cursor: pointer;
    font-size: 0.7vw;
    transition: background-color 0.2s;
}

#selector-select-all {
    background-color: #4CAF50;
    color: white;
}

#selector-select-all:hover {
    background-color: #45a049;
}

#selector-clear-all {
    background-color: #f44336;
    color: white;
}

#selector-clear-all:hover {
    background-color: #da190b;
}

#selector-confirm {
    background-color: #2196F3;
    color: white;
}

#selector-confirm:hover:not(:disabled) {
    background-color: #0b7dda;
}

#selector-confirm:disabled {
    background-color: #666666;
    cursor: not-allowed;
}

#selector-toggle-names {
    background-color: #FF9800;
    color: white;
}

#selector-toggle-names:hover {
    background-color: #F57C00;
}

.selector-main {
    flex: 1;
    display: flex;
    min-height: 0;
}

.map-container {
    flex: 4;
    position: relative;
    background-color: #05202e;
}

#network-selector-canvas {
    width: 100%;
    height: 100%;
    cursor: move;
}

.zoom-controls {
    position: absolute;
    top: 0.5vh;
    right: 0.5vw;
    display: flex;
    flex-direction: column;
    gap: 0.25vh;
}

.zoom-btn {
    width: 2vw;
    height: 2vw;
    border: none;
    border-radius: 0.2vw;
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-size: 0.9vw;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.zoom-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.selector-sidebar {
    flex: 1;
    min-width: 15vw;
    background-color: #102735;
    padding: 1vh;
    border-left: 0.05vw solid rgba(255, 255, 255, 0.1);
    overflow-y: auto;
}

.selector-sidebar h4 {
    margin: 0 0 0.8vh 0;
    color: #ffffff;
    font-size: 0.8vw;
}

.selector-sidebar h5 {
    margin: 1vh 0 0.5vh 0;
    color: #ffffff;
    font-size: 0.7vw;
}

.selector-sidebar h6 {
    margin: 0.8vh 0 0.4vh 0;
    color: #cccccc;
    font-size: 0.65vw;
    font-weight: bold;
}

.selected-list {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 0.2vw;
    padding: 0.8vh;
    margin-bottom: 1vh;
    min-height: 10vh;
    color: #ffffff;
}

.selected-list ul {
    margin: 0.25vh 0;
    padding-left: 1vw;
}

.selected-list li {
    margin: 0.15vh 0;
    font-size: 0.65vw;
    color: #cccccc;
}

.empty-message {
    text-align: center;
    color: #888888;
    font-style: italic;
    margin: 1vh 0;
}

.instructions {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 0.2vw;
    padding: 0.8vh;
}

.instructions ul {
    margin: 0.5vh 0;
    padding-left: 1vw;
    color: #cccccc;
}

.instructions li {
    margin: 0.25vh 0;
    font-size: 0.65vw;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .network-selector-modal {
        width: 95%;
        height: 95%;
    }

    .selector-main {
        flex-direction: column;
    }

    .map-container {
        height: 60%;
    }

    .selector-sidebar {
        height: 40%;
        min-width: unset;
        border-left: none;
        border-top: 0.05vw solid rgba(255, 255, 255, 0.1);
    }

    .selector-controls {
        flex-direction: column;
        gap: 0.5vh;
        align-items: stretch;
    }

    .selector-controls .info {
        justify-content: center;
    }

    .selector-controls .buttons {
        justify-content: center;
    }
}

/* 嵌入式选择器容器 - 显示在页面内部而非全屏覆盖 */
.network-selector-embedded {
    width: 100%;
    height: 100%; /* 改为100%以适应父容器大小 */
    margin-top: 0.4vh;
}

.network-selector-embedded .network-selector-modal {
    width: 100%;
    height: 100%;
    max-width: unset;
}
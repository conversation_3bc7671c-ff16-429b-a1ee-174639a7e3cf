<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大型活动交通组织管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #05202e;
            color: #95D7E3;
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        .test-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.8);
            z-index: 9999;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
        }

        .test-info {
            background: rgba(48, 171, 232, 0.1);
            border: 0.2vw solid #30abe8;
            border-radius: 1vw;
            padding: 2vh 3vw;
            text-align: center;
            max-width: 60vw;
        }

        .test-info h1 {
            font-size: 2vw;
            color: #30abe8;
            margin-bottom: 1vh;
        }

        .test-info p {
            font-size: 1vw;
            margin-bottom: 1vh;
            line-height: 1.5;
        }

        .zoom-display {
            background: rgba(255, 198, 25, 0.2);
            border: 0.1vw solid #ffc619;
            border-radius: 0.5vw;
            padding: 1vh 2vw;
            margin: 1vh 0;
            font-size: 1.2vw;
            color: #ffc619;
            font-weight: bold;
        }
    </style>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="network_selector.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />















</head>
<body>
    <!-- 测试覆盖层 -->
    <div class="test-overlay" id="testOverlay" style="display: none;">
        <div class="test-info">
            <h1>🎯 vw/vh 视口单位方案 - 一致性验证</h1>
            <p>index.html 和 final-test.html 现在使用完全一致的 vw/vh 单位方案</p>

            <div class="zoom-display" id="zoomDisplay">
                当前缩放: 100% | 视口: 1920×1080
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 修改为水平布局 -->
        <main>
            <div class="left-panel">
                <!-- 配置部分的内容 -->
                <section class="card" id="networkSection">
                    <h2>大型活动场景</h2>
                    <div class="option-group">
                        <div class="option">
                            <input type="radio" id="net-preset" name="network" value="preset" checked>
                            <label for="net-preset">雄安体育场大型体育赛事</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="net-custom" name="network" value="custom">
                            <label for="net-custom">自定义场景</label>
                        </div>
                        <button class="upload-btn" id="uploadNetBtn">
                            <i class="bi bi-upload"></i> 加载文件(*.net.xml)
                        </button>
                    </div>
                </section>

                <section class="card" id="trafficSection">
                    <h2>交通需求</h2>
                    <div class="option-group">
                        <div class="option">
                            <input type="radio" id="traffic-preset" name="traffic" value="preset" checked>
                            <label for="traffic-preset">预设</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="traffic-custom" name="traffic" value="custom">
                            <label for="traffic-custom">自定义</label>
                        </div>
                        <button class="upload-btn" id="uploadRouBtn">
                            <i class="bi bi-upload"></i> 加载文件(*.rou.xml)
                        </button>
                    </div>
                    <div class="sub-options">
                        <div class="vehicle-time-container">
                            <div class="sub-option">
                                <span class="bullet">•</span>
                                <label>车辆类型：</label>
                                <div class="select-wrapper">
                                    <select id="vehicleType">
                                        <option value="general">一般车辆</option>
                                        <option value="vip">贵宾专车</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="vehicle-time-container">
                            <div class="sub-option">
                                <span class="bullet">•</span>
                                <label>交通规模：</label>
                                <div class="select-wrapper">
                                    <select id="trafficScale">
                                        <option value="small">小</option>
                                        <option value="medium" selected>中</option>
                                        <option value="large">大</option>
                                    </select>
                                </div>
                            </div>
                            <div class="sub-option">
                                <span class="bullet">•</span>
                                <label>组织时段：</label>
                                <div class="select-wrapper">
                                    <select id="timePhase">
                                        <option value="entrance">进场</option>
                                        <option value="exit">离场</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="card" id="organizationSection">
                    <h2>组织方案</h2>
                    <div class="sub-options">
                        <div class="org-options-layout">
                            <!-- 第一列：道路相关选项 -->
                            <div class="org-column">
                                <div class="org-group">
                                    <div class="sub-option">
                                        <span class="bullet">•</span>
                                        <label>出入口方案：</label>
                                        <div class="select-wrapper">
                                            <button class="select-btn" id="selectEntranceBtn">
                                                <i class="bi bi-geo-alt"></i> 选择开放的出入口
                                            </button>
                                            <span id="entranceSelectionStatus" class="selection-status"></span>
                                        </div>
                                    </div>
                                    <div class="sub-option">
                                        <span class="bullet">•</span>
                                        <label>道路限行：</label>
                                        <div class="checkbox-wrapper">
                                            <input type="checkbox" id="roadRestriction">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 第二列：车辆和信号相关选项 -->
                            <div class="org-column">
                                <div class="org-group">
                                    <div class="sub-option">
                                        <span class="bullet">•</span>
                                        <label>贵宾专车优先通行：</label>
                                        <div class="checkbox-wrapper">
                                            <input type="checkbox" id="vipPriority">
                                        </div>
                                    </div>
                                </div>

                                <div class="org-group signal-group">
                                    <div class="sub-option">
                                        <span class="bullet">•</span>
                                        <label>信号配时：</label>
                                        <div class="signal-options">
                                            <div class="option-group compact">
                                                <div class="option">
                                                    <input type="radio" id="signal-preset" name="signal" value="preset" checked>
                                                    <label for="signal-preset">预设</label>
                                                </div>
                                                <div class="option">
                                                    <input type="radio" id="signal-custom" name="signal" value="custom">
                                                    <label for="signal-custom">自定义</label>
                                                </div>
                                            </div>
                                            <button class="upload-btn" id="uploadAddBtn">
                                                <i class="bi bi-upload"></i> 加载文件(*.add.xml)
                                            </button>

                                        </div>
                                    </div>
                                    <div class="sub-option">
                                        <span class="bullet">•</span>
                                        <label>信控优化：</label>
                                        <div class="checkbox-wrapper">
                                            <input type="checkbox" id="signalOptimization">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="card" id="analysisSection">
                    <h2>分析配置</h2>
                    <div class="sub-options">
                        <div class="sub-option">
                            <span class="bullet">•</span>
                            <label>用户自选路段指标分析：</label>
                            <div class="checkbox-wrapper">
                                <input type="checkbox" id="edgeAnalysis">
                            </div>
                            <span id="edgeAnalysisStatus" class="analysis-status"></span>
                        </div>
                        <div id="edgeAnalysisDetails" class="edge-analysis-details analysis-details">
                            <div class="sub-option">
                                <button class="upload-btn" id="selectEdgesBtn">
                                    <i class="bi bi-geo-alt"></i> 选择分析路段
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <div class="action-buttons">
                    <button class="run-btn">运行方案</button>
                    <button class="load-btn" id="loadResultBtn">
                        <i class="bi bi-file-earmark-text"></i> 加载已有结果
                    </button>
                    <button class="history-btn" id="historyBtn">
                        <i class="bi bi-clock-history"></i> 历史方案
                    </button>
                </div>
            </div>

            <!-- 右侧主内容区 - 左右两栏布局：左侧为地图和仿真概况，右侧为指标图表 -->
            <div class="right-panel">
                <!-- 左侧区域：地图和仿真概况（垂直排列） -->
                <div class="map-section">
                    <!-- 固定的选择器/地图容器 -->
                    <div id="fixed-selector-container" class="fixed-selector-container map-container">
                        <!-- 默认显示地图 -->
                        <div id="default-map-view" class="default-map-view">
                            <div class="map-placeholder">
                                <i class="bi bi-map"></i>
                                <p>路网地图</p>
                            </div>
                        </div>
                        <!-- 选择器将在需要时显示在这里 -->
                        <div id="embedded-selector-view" class="embedded-selector-view"></div>
                    </div>

                    <!-- 仿真概况与配置 -->
                    <section class="result-card simulation-info-config" id="simulationInfoConfig">
                        <div class="result-header">
                            <h2 class="simulation-title">仿真概况与配置</h2>
                            <div class="result-actions">
                                <button id="compareButton" class="compare-action-btn">
                                    <i class="bi bi-bar-chart"></i> 方案对比
                                </button>
                            </div>
                        </div>
                        <div class="simulation-info">
                            <div>
                                <span><strong>仿真ID:</strong> <span id="simId">250702174456</span></span>
                                <span class="sim-time"><strong>开始时间:</strong> <span id="startTime">2025-07-02 17:44:56</span></span>
                            </div>
                        </div>
                        <!-- 新增的对比方案信息显示栏 -->
                        <div id="comparisonSchemesInfo" class="comparison-schemes-info">
                            当前对比方案：无
                        </div>
                        <div id="comparisonSchemesSummaryList" class="comparison-schemes-summary">
                            <!-- 这里由 JS 填充每个方案的配置摘要 -->
                        </div>
                        <div class="config-summary">
                            <div class="config-comparison">
                                <div class="current-config">
                                    <h3>当前方案</h3>
                                    <div class="config-item" id="networkConfig">
                                        <span><strong>路网配置:</strong> <span>预设路网 - 仅开放东侧出入口 + 道路限行(2个路段)</span></span>
                                    </div>
                                    <div class="config-item" id="signalConfig">
                                        <span><strong>信号配置:</strong> <span>预设配时 + 自定义优化(2个交叉口)</span></span>
                                    </div>
                                    <div class="config-item" id="trafficConfig">
                                        <span><strong>交通需求:</strong> <span>预设需求 - 进场场景 + 中规模 + 贵宾专车</span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>

                <!-- 右侧区域：指标图表 -->
                <div class="charts-section">
                    <div class="charts-header">
                        <h3>交通指标分析</h3>
                    </div>
                    <div class="charts-container">
                            <section class="result-card">
                                <div class="result-header">
                                    <h2 class="chart-title">平均行程时间对比</h2>
                                </div>
                                <div class="chart-container">
                                    <canvas id="travelTimeChart"></canvas>
                                </div>
                            </section>

                            <section class="result-card">
                                <div class="result-header">
                                    <h2 class="chart-title">平均等待时间对比</h2>
                                </div>
                                <div class="chart-container">
                                    <canvas id="waitingTimeChart"></canvas>
                                </div>
                            </section>

                            <section class="result-card">
                                <div class="result-header">
                                    <h2 class="chart-title">平均等待次数对比</h2>
                                </div>
                                <div class="chart-container">
                                    <canvas id="waitingCountChart"></canvas>
                                </div>
                            </section>

                            <section class="result-card">
                                <div class="result-header">
                                    <h2 class="chart-title">时间损失与延误对比</h2>
                                </div>
                                <div class="chart-container">
                                    <canvas id="timeLossDelayChart"></canvas>
                                </div>
                            </section>

                            <!-- 添加路段分析结果显示区域 -->
                            <section class="result-card edge-analysis-section" id="edgeAnalysisSection">
                                <div class="result-header">
                                    <h2 class="chart-title">用户自选路段分析结果</h2>
                                </div>
                                <div class="edge-analysis-container">
                                    <div class="edge-analysis-column">
                                        <h3 class="metrics-subtitle">行人路段指标</h3>
                                        <div class="metrics-grid edge-metrics-grid">
                                            <div class="metric-item edge-metric-item">
                                                <div class="metric-value" id="edgePedTravelTime">-</div>
                                                <div class="metric-label">平均行程时间 (秒)</div>
                                            </div>
                                            <div class="metric-item edge-metric-item">
                                                <div class="metric-value" id="edgePedWaitingTime">-</div>
                                                <div class="metric-label">平均等待时间 (秒)</div>
                                            </div>
                                            <div class="metric-item edge-metric-item">
                                                <div class="metric-value" id="edgePedTimeLoss">-</div>
                                                <div class="metric-label">平均时间损失 (秒)</div>
                                            </div>
                                            <div class="metric-item edge-metric-item">
                                                <div class="metric-value" id="edgePedSpeed">-</div>
                                                <div class="metric-label">平均速度 (m/s)</div>
                                            </div>
                                            <div class="metric-item edge-metric-item">
                                                <div class="metric-value" id="edgePedCount">-</div>
                                                <div class="metric-label">总进入数 (人次)</div>
                                            </div>
                                            <div class="metric-item edge-metric-item">
                                                <div class="metric-value" id="edgePedOccupancy">-</div>
                                                <div class="metric-label">平均占用率 (%)</div>
                                            </div>
                                        </div>
                                        <div class="chart-container edge-chart-container">
                                            <canvas id="edgePedChart"></canvas>
                                        </div>
                                    </div>
                                    <div class="edge-analysis-column">
                                        <h3 class="metrics-subtitle">车辆路段指标</h3>
                                        <div class="metrics-grid edge-metrics-grid">
                                            <div class="metric-item edge-metric-item">
                                                <div class="metric-value" id="edgeVehTravelTime">-</div>
                                                <div class="metric-label">平均行程时间 (秒)</div>
                                            </div>
                                            <div class="metric-item edge-metric-item">
                                                <div class="metric-value" id="edgeVehWaitingTime">-</div>
                                                <div class="metric-label">平均等待时间 (秒)</div>
                                            </div>
                                            <div class="metric-item edge-metric-item">
                                                <div class="metric-value" id="edgeVehTimeLoss">-</div>
                                                <div class="metric-label">平均时间损失 (秒)</div>
                                            </div>
                                            <div class="metric-item edge-metric-item">
                                                <div class="metric-value" id="edgeVehSpeed">-</div>
                                                <div class="metric-label">平均速度 (m/s)</div>
                                            </div>
                                            <div class="metric-item edge-metric-item">
                                                <div class="metric-value" id="edgeVehCount">-</div>
                                                <div class="metric-label">总进入数 (车次)</div>
                                            </div>
                                            <div class="metric-item edge-metric-item">
                                                <div class="metric-value" id="edgeVehOccupancy">-</div>
                                                <div class="metric-label">平均占用率 (%)</div>
                                            </div>
                                        </div>
                                        <div class="chart-container edge-chart-container">
                                            <canvas id="edgeVehChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
                

            </div>
        </main>
    </div>

    <!-- 历史方案模态框 -->
    <div id="historyModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>历史方案管理</h3>
                <span class="close" id="closeHistoryModal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="history-controls">
                    <button id="saveCurrentBtn" class="save-btn">
                        <i class="bi bi-floppy"></i> 保存当前配置
                    </button>
                    <button id="refreshHistoryBtn" class="refresh-btn">
                        <i class="bi bi-arrow-clockwise"></i> 刷新列表
                    </button>
                </div>

                <div class="history-list" id="historyList">
                    <div class="loading-message">正在加载历史方案...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 保存方案模态框 -->
    <div id="saveSchemeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>保存配置方案</h3>
                <span class="close" id="closeSaveModal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="schemeName">方案名称：</label>
                    <input type="text" id="schemeName" placeholder="请输入方案名称（可选）">
                </div>
                <div class="form-group">
                    <label for="schemeDescription">方案描述：</label>
                    <textarea id="schemeDescription" placeholder="请输入方案描述（可选）" rows="3"></textarea>
                </div>
                <div class="config-preview">
                    <h4>配置预览：</h4>
                    <div id="configPreview" class="config-summary"></div>
                </div>
                <div class="modal-actions">
                    <button id="confirmSaveBtn" class="confirm-btn">确认保存</button>
                    <button id="cancelSaveBtn" class="cancel-btn">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script src="debug.js"></script>
    <script src="script.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 构建配置JSON - 供script.js中的startSimulation函数使用
            window.buildConfigJSON = function() {
                // 获取路网配置
                const networkType = document.querySelector('input[name="network"]:checked').value;
                const entranceType = getEntrancePlanFromSelection();
                const roadRestriction = document.getElementById('roadRestriction').checked;

                // 获取信号配置
                const signalType = document.querySelector('input[name="signal"]:checked').value;
                const signalOptimization = document.getElementById('signalOptimization').checked;

                // 获取交通需求配置
                const trafficType = document.querySelector('input[name="traffic"]:checked').value;
                const vehicleType = document.getElementById('vehicleType').value;
                const trafficScale = document.getElementById('trafficScale').value;
                const timePhase = document.getElementById('timePhase').value;
                const vipPriority = document.getElementById('vipPriority').checked;


                // 获取分析配置
                const edgeAnalysis = document.getElementById('edgeAnalysis').checked;

                // 构建完整配置对象
                return {
                    "network_config": {
                        "type": networkType === "preset" ? "predefined" : "custom",
                        "file_path": networkType === "custom" ? "custom_network.net.xml" : null,
                        "entrance_plan": entranceType,
                        "road_restriction": {
                            "enabled": roadRestriction,
                            "restricted_edges": roadRestriction ? getRestrictedEdges() : []
                        }
                    },
                    "signal_config": {
                        "type": signalType === "preset" ? "predefined" : "custom",
                        "file_path": signalType === "custom" ? "custom_signal.add.xml" : null,
                        "optimization": {
                            "enabled": signalOptimization,
                            "selected_intersections": signalOptimization ? getSelectedIntersections() : []
                        }
                    },
                    "traffic_config": {
                        "type": trafficType === "preset" ? "predefined" : "custom",
                        "file_path": trafficType === "custom" ? "custom_traffic.rou.xml" : null,
                        "scenario": timePhaseToText(timePhase),
                        "vehicle_type": vehicleTypeToText(vehicleType),
                        "traffic_scale": trafficScale,
                        "vip_priority": {
                            "enabled": vipPriority
                        }
                    },
                    "analysis_config": {
                        "edge_analysis": {
                            "enabled": edgeAnalysis,
                            "selected_edges": edgeAnalysis ? getSelectedAnalysisEdges() : []
                        }
                    }
                };
            }

            // 获取出入口方案文本描述
            function getEntrancePlanFromSelection() {
                const selectedEdges = window.selectedEntranceEdges || [];

                if (selectedEdges.length === 0) {
                    return '仅开放东侧出入口'; // 默认值
                }

                // 检查是否选择了东侧出入口路段
                const hasEastEntrance = selectedEdges.some(edge => 
                    edge === 'people_east_in' || edge === 'people_east_out'
                );

                // 检查是否选择了南侧出入口路段  
                const hasSouthEntrance = selectedEdges.some(edge => 
                    edge === 'people_south_in' || edge === 'people_south_out'
                );

                // 根据选择情况决定出入口方案
                if (hasEastEntrance && hasSouthEntrance) {
                    return '全部开放';
                } else if (hasSouthEntrance && !hasEastEntrance) {
                    return '仅开放南侧出入口';
                } else {
                    return '仅开放东侧出入口';
                }
            }

            // 辅助函数，转换车辆类型选择为文本
            function vehicleTypeToText(type) {
                switch(type) {
                    case "general": return "仅一般车辆";
                    case "vip": return "存在贵宾专车";
                    default: return "仅一般车辆";
                }
            }

            // 辅助函数，转换时段选择为文本
            function timePhaseToText(phase) {
                switch(phase) {
                    case "entrance": return "进场";
                    case "exit": return "离场";
                    default: return "进场";
                }
            }

            // 获取已选择的交叉口列表（从全局状态获取）
            function getSelectedIntersections() {
                // 从script.js中的全局变量获取
                // 注意：如果选择器返回了混合结果（交叉口+路段），只返回交叉口部分
                const intersections = window.selectedIntersections || [];
                const optimizationEdges = window.selectedOptimizationEdges || [];
                
                // 如果存在优化路段，说明是混合选择，需要合并返回
                if (optimizationEdges.length > 0) {
                    return {
                        intersections: intersections,
                        edges: optimizationEdges
                    };
                }
                
                // 否则只返回交叉口（保持向后兼容）
                return intersections;
            }

            // 获取已选择的限行路段列表（从全局状态获取）
            function getRestrictedEdges() {
                // 从script.js中的全局变量获取
                return window.selectedRestrictedEdges || [];
            }
            // 获取已选择的分析路段列表（从全局状态获取）
            function getSelectedAnalysisEdges() {
                // 从script.js中的全局变量获取
                return window.selectedAnalysisEdges || [];
            }

            // 构建结果数据，适配新的JSON格式
            window.navigateToResultPage = function(data) {
                // 构建结果数据，适配新的JSON格式
                const resultData = {
                    "simulation_id": data.simulation_id,
                    "config_summary": data.config_summary,
                    "start_time": new Date().toISOString(),  // 使用当前时间
                    "simulation_results": data.metrics  // 直接使用后端返回的新格式metrics
                };

                // 将数据编码为URL参数
                const encodedData = encodeURIComponent(JSON.stringify(resultData));

                // 跳转到结果页面
                window.location.href = `results.html?data=${encodedData}`;
            }

            // 显示错误信息 - 供script.js使用
            window.showError = function(message) {
                alert(`错误: ${message}`);
            }

            // ==================== 路网地图加载功能 ====================

            // 主初始化函数 - 调用 script.js 中已有的地图功能
            function initializeNetworkMapLoader() {
                console.log('开始初始化路网地图加载器...');

                // 检查必要的依赖
                if (typeof L === 'undefined') {
                    console.error('Leaflet 库未加载，延迟重试...');
                    setTimeout(initializeNetworkMapLoader, 1000);
                    return;
                }

                // 检查 script.js 中的地图初始化函数是否可用
                if (typeof window.initializeMap === 'function') {
                    console.log('使用 script.js 中的地图初始化函数');
                    setTimeout(() => {
                        try {
                            window.initializeMap();
                        } catch (error) {
                            console.error('调用 script.js 地图初始化函数失败:', error);
                        }
                    }, 2000);
                } else {
                    console.log('script.js 中的地图初始化函数不可用，使用简化版本');
                    setTimeout(initializeSimpleMap, 2000);
                }
            }

            // 简化的地图初始化函数（备用方案）
            function initializeSimpleMap() {
                console.log('初始化简化版地图...');

                // 获取地图容器
                const mapContainer = document.querySelector('.map-placeholder');
                if (!mapContainer) {
                    console.error('找不到地图容器 .map-placeholder');
                    return;
                }

                // 更新占位符显示
                mapContainer.innerHTML = '<div class="map-loading"><i class="bi bi-map"></i><p>正在加载路网地图...</p></div>';

                // 尝试调用 script.js 中的其他地图相关函数
                setTimeout(() => {
                    if (typeof window.initializeDefaultMap === 'function') {
                        console.log('调用 initializeDefaultMap 函数');
                        window.initializeDefaultMap();
                    } else {
                        console.log('未找到可用的地图初始化函数');
                        mapContainer.innerHTML = '<div class="map-placeholder"><i class="bi bi-map"></i><p>路网地图</p><small>请点击"运行方案"查看仿真地图</small></div>';
                    }
                }, 1000);
            }

            // 添加窗口大小变化时重新绘制图表的监听器
            window.addEventListener('resize', function() {
                // 如果已经存在图表实例，则在窗口调整大小后重新渲染
                if (window.pedestrianChart instanceof Chart) {
                    window.pedestrianChart.resize();
                }
                if (window.vehicleChart instanceof Chart) {
                    window.vehicleChart.resize();
                }
                if (window.vipVehicleChart instanceof Chart) {
                    window.vipVehicleChart.resize();
                }
                if (window.venueChart instanceof Chart) {
                    window.venueChart.resize();
                }
                if (window.edgePedChart instanceof Chart) {
                    window.edgePedChart.resize();
                }
                if (window.edgeVehChart instanceof Chart) {
                    window.edgeVehChart.resize();
                }
            });

            // 初始化路网地图加载功能
            initializeNetworkMapLoader();

            // 只有当页面包含结果数据参数时才加载和显示结果
            if (window.location.search.includes('data=')) {
                loadResultData().then(data => {
                    updateDisplay(data);
                    // 保存已加载的结果数据
                    currentLoadedResultData = JSON.parse(JSON.stringify(data));
                });
            } else {
                // 如果没有携带 data 参数，加载默认结果数据并更新展示，
                // 确保 currentDisplayedData 与 currentLoadedResultData 初始化完成，
                // 以防后续方案对比中出现"随机"数值。
                loadResultData().then(data => {
                    updateDisplay(data);
                    // 保存已加载的结果数据
                    currentLoadedResultData = JSON.parse(JSON.stringify(data));
                });
            }

            // 添加方案对比功能
            document.getElementById('historyBtn').addEventListener('click', function() {
                document.getElementById('historyModal').style.display = 'block';
                getDefaultResultData();
                document.getElementById('refreshHistoryBtn').click();
            });

            // // 直接方案对比按钮
            //  document.getElementById('compareButton').addEventListener('click', function() {
            //      // 确保保存当前显示的数据
            //      console.log("点击方案对比按钮，当前显示数据状态:", 
            //                  currentDisplayedData ? "已存在" : "不存在",
            //                  "当前加载数据状态:", 
            //                  currentLoadedResultData ? "已存在" : "不存在");
                
            //      if (currentDisplayedData) {
            //          console.log("使用当前显示数据:", currentDisplayedData.simulation_id);
            //          // 深度复制确保数据独立
            //          currentLoadedResultData = JSON.parse(JSON.stringify(currentDisplayedData));
            //          console.log("已保存到currentLoadedResultData:", currentLoadedResultData.simulation_id);
            //      } else if (currentLoadedResultData) {
            //          console.log("没有当前显示数据，但有当前加载数据:", currentLoadedResultData.simulation_id);
            //      } else {
            //          console.log("警告：没有可用于对比的当前数据");
            //      }
                
            //      document.getElementById('historyModal').style.display = 'block';
            //      loadHistorySchemes();
            //      // 显示提示
            //      alert("请从历史方案中选择一个方案进行对比");
            //  });
            // 直接方案对比按钮：弹窗并刷新
            document.getElementById('compareButton').addEventListener('click', function() {
                document.getElementById('historyModal').style.display = 'block';
                getDefaultResultData();
                document.getElementById('refreshHistoryBtn').click();
                
            });

           
            


            // 关闭历史方案对话框
            document.getElementById('closeHistoryModal').addEventListener('click', function() {
                document.getElementById('historyModal').style.display = 'none';
            });

            // 加载历史方案列表
            function loadHistorySchemes() {
                const historyList = document.getElementById('historyList');
                historyList.innerHTML = '<div class="loading-message">正在加载历史方案...</div>';

                // 模拟获取历史方案数据
                setTimeout(function() {
                    const sampleSchemes = [
                        {id: "250702174456", name: "方案A - 东侧出入口", date: "2025-07-02 17:44:56"},
                        {id: "250702185532", name: "方案B - 南侧出入口", date: "2025-07-02 18:55:32"},
                        {id: "250703093022", name: "方案C - 全部开放", date: "2025-07-03 09:30:22"}
                    ];

                    if (sampleSchemes.length === 0) {
                        historyList.innerHTML = '<div class="empty-message">暂无历史方案</div>';
                        return;
                    }

                    let html = '';
                    sampleSchemes.forEach(scheme => {
                        html += `
                        <div class="history-item">
                            <div class="history-info">
                                <div class="history-name">${scheme.name}</div>
                                <div class="history-date">${scheme.date}</div>
                            </div>
                            <div class="history-actions">
                                <button class="view-btn" data-id="${scheme.id}">查看</button>
                                <button class="compare-btn" data-id="${scheme.id}">对比</button>
                                <button class="delete-btn" data-id="${scheme.id}">删除</button>
                            </div>
                        </div>`;
                    });

                    historyList.innerHTML = html;

                    // 添加按钮事件监听
                    document.querySelectorAll('.view-btn').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const schemeId = this.getAttribute('data-id');
                            viewScheme(schemeId);
                        });
                    });

                    document.querySelectorAll('.compare-btn').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const schemeId = this.getAttribute('data-id');
                            compareScheme(schemeId);
                        });
                    });

                    document.querySelectorAll('.delete-btn').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const schemeId = this.getAttribute('data-id');
                            deleteScheme(schemeId);
                        });
                    });
                }, 1000);
            }

            // 查看方案详情
            function viewScheme(schemeId) {
                console.log("查看方案:", schemeId);
                // 模拟加载方案数据并显示
                loadSchemeData(schemeId).then(data => {
                    updateDisplay(data);
                    document.getElementById('historyModal').style.display = 'none';
                });
            }

            // 添加一个全局变量存储当前已加载的结果数据
            var currentLoadedResultData = null;
            // 添加一个变量存储已加载的历史方案数据
            var loadedSchemeData = {};
            // 添加一个变量用于存储当前页面上显示的指标数据
            var currentDisplayedData = null;

            // 对比方案
            function compareScheme(schemeId) {
                console.log("开始对比方案:", schemeId);
                console.log("对比开始时数据状态 - 当前显示数据:", 
                            currentDisplayedData ? currentDisplayedData.simulation_id : "不存在", 
                            "当前加载数据:", 
                            currentLoadedResultData ? currentLoadedResultData.simulation_id : "不存在");
                
                // 使用已缓存的当前数据或加载数据
                const getCurrentData = async () => {
                    // 如果已经有显示的数据，直接使用
                    if (currentDisplayedData) {
                        console.log("使用当前显示的数据进行对比:", currentDisplayedData.simulation_id);
                        return JSON.parse(JSON.stringify(currentDisplayedData)); // 返回完全独立的深拷贝
                    }
                    // 如果已经有加载的数据，直接使用
                    else if (currentLoadedResultData) {
                        console.log("使用已加载的真实数据进行对比:", currentLoadedResultData.simulation_id);
                        return JSON.parse(JSON.stringify(currentLoadedResultData)); // 返回完全独立的深拷贝
                    } else {
                        // 否则尝试加载数据
                        console.log("没有缓存数据，尝试重新加载");
                        return await loadResultData();
                    }
                };

                // 加载当前方案和选定方案的数据
                Promise.all([
                    getCurrentData(),
                    loadSchemeData(schemeId)
                ]).then(([currentData, compareData]) => {
                    console.log("已获取当前方案数据:", currentData.simulation_id);
                    console.log("已获取对比方案数据:", compareData.simulation_id);
                    
                    // 确保使用完全独立的数据对象
                    showComparison(
                        JSON.parse(JSON.stringify(currentData)),
                        JSON.parse(JSON.stringify(compareData))
                    );
                    document.getElementById('historyModal').style.display = 'none';
                });
            }

            // 删除方案
            function deleteScheme(schemeId) {
                if (confirm(`确定要删除方案 ${schemeId} 吗？`)) {
                    console.log("删除方案:", schemeId);
                    // 模拟删除操作
                    alert("方案已删除");
                    loadHistorySchemes(); // 重新加载列表
                }
            }

            // 加载指定方案数据（通过用户选择 JSON 文件，而非预设示例）
            async function loadSchemeData(schemeId) {
                console.log("开始加载对比方案数据，schemeId:", schemeId);

                // 如果已经缓存该方案，直接返回
                if (loadedSchemeData[schemeId]) {
                    console.log("使用缓存的方案数据:", schemeId);
                    return JSON.parse(JSON.stringify(loadedSchemeData[schemeId]));
                }

                // 采用与 script.js 中 loadExistingResult 相同的文件选择逻辑
                return new Promise((resolve, reject) => {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.json';

                    input.onchange = function(e) {
                        const file = e.target.files[0];
                        if (!file) {
                            reject(new Error('未选择文件'));
                            return;
                        }

                        // 验证扩展名
                        if (!file.name.toLowerCase().endsWith('.json')) {
                            alert('文件类型错误，请选择JSON结果文件');
                            reject(new Error('文件类型错误'));
                            return;
                        }

                        const reader = new FileReader();
                        reader.onload = function(evt) {
                            try {
                                const jsonData = JSON.parse(evt.target.result);

                                // 使用 script.js 中的验证与标准化工具函数
                                if (typeof validateResultJson === 'function' && !validateResultJson(jsonData)) {
                                    alert('JSON格式无效，请提供有效的结果文件');
                                    reject(new Error('JSON格式无效'));
                                    return;
                                }

                                let normalizedData = jsonData;
                                if (typeof normalizeResultJson === 'function') {
                                    normalizedData = normalizeResultJson(jsonData);
                                }

                                // 若缺少摘要或时间，补充
                                if (!normalizedData.config_summary && typeof generateConfigSummaryFromConfig === 'function' && normalizedData.config) {
                                    normalizedData.config_summary = generateConfigSummaryFromConfig(normalizedData.config);
                                }
                                if (!normalizedData.start_time) {
                                    normalizedData.start_time = new Date().toISOString();
                                }

                                // 使用文件名作为 simulation_id（若未提供）
                                if (!normalizedData.simulation_id) {
                                    normalizedData.simulation_id = file.name.replace(/\.json$/i, '');
                                }

                                // 缓存并返回
                                loadedSchemeData[schemeId] = JSON.parse(JSON.stringify(normalizedData));
                                resolve(normalizedData);
                            } catch (err) {
                                console.error('解析JSON文件失败:', err);
                                alert('无法解析JSON文件');
                                reject(err);
                            }
                        };

                        reader.onerror = function() {
                            alert('读取文件时发生错误');
                            reject(new Error('文件读取错误'));
                        };

                        reader.readAsText(file);
                    };

                    // 触发文件选择
                    input.click();
                });
            }

            // 显示方案对比
            function showComparison(currentData, compareData) {
                // 添加对比标题
                addComparisonHeader(compareData.simulation_id);

                // 创建对比图表
                createComparisonCharts(currentData, compareData);

                // 显示对比方案的配置
                displayComparisonConfig(compareData);
            }

            // 显示对比方案的配置信息
            function displayComparisonConfig(compareData) {
                // 显示对比方案的ID
                document.getElementById('compareSchemeId').textContent = `(${compareData.simulation_id})`;

                // 显示对比方案的配置信息
                document.querySelector('#compareNetworkConfig span span').textContent = compareData.config_summary.network;
                document.querySelector('#compareSignalConfig span span').textContent = compareData.config_summary.signal;
                document.querySelector('#compareTrafficConfig span span').textContent = compareData.config_summary.traffic;

                // 显示对比配置区域
                document.querySelector('.compare-config').style.display = 'block';
            }

            // 添加对比标题
            function addComparisonHeader(compareId) {
                // 检查是否已存在对比标题
                let header = document.querySelector('.comparison-header');
                if (!header) {
                    header = document.createElement('div');
                    header.className = 'comparison-header';
                    header.innerHTML = `
                        <div class="comparison-title">正在与方案 ${compareId} 进行对比</div>
                        <button class="close-comparison">&times;</button>
                    `;

                    // 在仿真概况之后插入
                    const simulationInfo = document.getElementById('simulationInfoConfig');
                    simulationInfo.parentNode.insertBefore(header, simulationInfo.nextSibling);

                    // 添加关闭对比的事件
                    header.querySelector('.close-comparison').addEventListener('click', function() {
                        // 移除对比标题
                        header.remove();

                        // 隐藏对比配置
                        document.querySelector('.compare-config').style.display = 'none';

                        // 恢复原始图表 - 使用已缓存的数据而非重新加载
                        if (currentLoadedResultData) {
                            console.log("关闭对比，恢复原始数据显示:", currentLoadedResultData.simulation_id);
                            // 重要：传递深拷贝以避免引用问题
                            updateDisplay(JSON.parse(JSON.stringify(currentLoadedResultData)));
                        } else {
                            console.log("没有原始数据缓存，尝试重新加载");
                            // 如果没有缓存数据，才去加载
                            loadResultData().then(data => {
                                updateDisplay(data);
                            });
                        }
                    });
                } else {
                    // 更新对比标题
                    header.querySelector('.comparison-title').textContent = `正在与方案 ${compareId} 进行对比`;
                }
            }

            // 创建对比图表
            function createComparisonCharts(currentData, compareData) {
                console.log("创建对比图表，当前方案ID:", currentData.simulation_id, "对比方案ID:", compareData.simulation_id);

                // 兼容新/旧格式，提取四类指标
                function extractMetrics(data) {
                    if (data && data.simulation_results) {
                        if (data.simulation_results.network_metrics) {
                            // 新格式
                            return JSON.parse(JSON.stringify(data.simulation_results.network_metrics));
                        } else {
                            // 旧格式
                            return JSON.parse(JSON.stringify({
                                pedestrian_metrics: data.simulation_results.pedestrian_metrics || {},
                                vehicle_metrics: data.simulation_results.vehicle_metrics || {},
                                vip_vehicle_metrics: data.simulation_results.vip_vehicle_metrics || {},
                                venue_area_metrics: data.simulation_results.venue_area_metrics || {}
                            }));
                        }
                    }
                    // 如果数据格式异常，返回空对象
                    return {
                        vehicle_metrics: {},
                        pedestrian_metrics: {},
                        vip_vehicle_metrics: {},
                        venue_area_metrics: {}
                    };
                }

                const currentMetrics = extractMetrics(currentData);
                const compareMetrics = extractMetrics(compareData);

                console.log("对比图表使用的当前方案数据:", JSON.stringify(currentMetrics.vehicle_metrics));
                console.log("对比图表使用的比较方案数据:", JSON.stringify(compareMetrics.vehicle_metrics));
                
                // 确保所有指标都有默认值
                ensureAllMetricsDefaults(currentMetrics);
                ensureAllMetricsDefaults(compareMetrics);

                // 使用新的对比图表函数
                createComparisonTravelTimeChart(currentMetrics, compareMetrics, compareData.simulation_id);
                createComparisonWaitingTimeChart(currentMetrics, compareMetrics, compareData.simulation_id);
                createComparisonWaitingCountChart(currentMetrics, compareMetrics, compareData.simulation_id);
                createComparisonTimeLossDelayChart(currentMetrics, compareMetrics, compareData.simulation_id);
                
                // 添加对比自选路段指标
                createComparisonEdgeCharts(currentData, compareData);
            }

            // 创建车辆指标对比图
            function createComparisonVehicleChart(currentMetrics, compareMetrics, compareId) {
                const ctx = document.getElementById('vehChart').getContext('2d');

                // 销毁已存在的图表实例
                if (window.vehicleChart instanceof Chart) {
                    window.vehicleChart.destroy();
                }

                window.vehicleChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
                        datasets: [
                            {
                                label: '当前方案',
                                data: [
                                    currentMetrics.average_travel_time,
                                    currentMetrics.average_waiting_time,
                                    currentMetrics.average_waiting_count,
                                    currentMetrics.average_time_loss
                                ],
                                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            },
                            {
                                label: `对比方案 (${compareId})`,
                                data: [
                                    compareMetrics.average_travel_time,
                                    compareMetrics.average_waiting_time,
                                    compareMetrics.average_waiting_count,
                                    compareMetrics.average_time_loss
                                ],
                                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            },
                            x: {
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                labels: { color: '#ffffff' }
                            },
                            title: {
                                display: true,
                                text: '一般车辆指标对比',
                                color: '#ffffff'
                            }
                        }
                    }
                });
            }

            // 创建行人指标对比图
            function createComparisonPedestrianChart(currentMetrics, compareMetrics, compareId) {
                const ctx = document.getElementById('pedChart').getContext('2d');

                // 销毁已存在的图表实例
                if (window.pedestrianChart instanceof Chart) {
                    window.pedestrianChart.destroy();
                }

                window.pedestrianChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
                        datasets: [
                            {
                                label: '当前方案',
                                data: [
                                    currentMetrics.average_travel_time,
                                    currentMetrics.average_waiting_time,
                                    currentMetrics.average_waiting_count,
                                    currentMetrics.average_time_loss
                                ],
                                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            },
                            {
                                label: `对比方案 (${compareId})`,
                                data: [
                                    compareMetrics.average_travel_time,
                                    compareMetrics.average_waiting_time,
                                    compareMetrics.average_waiting_count,
                                    compareMetrics.average_time_loss
                                ],
                                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            },
                            x: {
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                labels: { color: '#ffffff' }
                            },
                            title: {
                                display: true,
                                text: '行人指标对比',
                                color: '#ffffff'
                            }
                        }
                    }
                });
            }

            // 创建贵宾车辆指标对比图
            function createComparisonVIPChart(currentMetrics, compareMetrics, compareId) {
                const ctx = document.getElementById('vipChart').getContext('2d');

                // 销毁已存在的图表实例
                if (window.vipVehicleChart instanceof Chart) {
                    window.vipVehicleChart.destroy();
                }

                window.vipVehicleChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
                        datasets: [
                            {
                                label: '当前方案',
                                data: [
                                    currentMetrics.average_travel_time,
                                    currentMetrics.average_waiting_time,
                                    currentMetrics.average_waiting_count,
                                    currentMetrics.average_time_loss
                                ],
                                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            },
                            {
                                label: `对比方案 (${compareId})`,
                                data: [
                                    compareMetrics.average_travel_time,
                                    compareMetrics.average_waiting_time,
                                    compareMetrics.average_waiting_count,
                                    compareMetrics.average_time_loss
                                ],
                                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            },
                            x: {
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                labels: { color: '#ffffff' }
                            },
                            title: {
                                display: true,
                                text: '贵宾专车指标对比',
                                color: '#ffffff'
                            }
                        }
                    }
                });
            }

            // 创建场馆区域指标对比图
            function createComparisonVenueChart(currentMetrics, compareMetrics, compareId) {
                const ctx = document.getElementById('venueChart').getContext('2d');

                // 销毁已存在的图表实例
                if (window.venueChart instanceof Chart) {
                    window.venueChart.destroy();
                }

                window.venueChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['行人平均行程时间', '行人平均延误', '车辆平均行程时间', '车辆平均延误'],
                        datasets: [
                            {
                                label: '当前方案',
                                data: [
                                    currentMetrics.average_pedestrian_travel_time,
                                    currentMetrics.average_pedestrian_delay,
                                    currentMetrics.average_vehicle_travel_time,
                                    currentMetrics.average_vehicle_delay
                                ],
                                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            },
                            {
                                label: `对比方案 (${compareId})`,
                                data: [
                                    compareMetrics.average_pedestrian_travel_time,
                                    compareMetrics.average_pedestrian_delay,
                                    compareMetrics.average_vehicle_travel_time,
                                    compareMetrics.average_vehicle_delay
                                ],
                                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            },
                            x: {
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: '#ffffff' }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                labels: { color: '#ffffff' }
                            },
                            title: {
                                display: true,
                                text: '场馆区域指标对比',
                                color: '#ffffff'
                            }
                        }
                    }
                });
            }

            // 创建路段对比时的图表更新函数
            function createComparisonEdgeCharts(currentData, compareData) {
                if (currentData && compareData && 
                    currentData.simulation_results && compareData.simulation_results &&
                    currentData.simulation_results.selected_edge_metrics && compareData.simulation_results.selected_edge_metrics) {
                    
                    const currentEdgeMetrics = currentData.simulation_results.selected_edge_metrics;
                    const compareEdgeMetrics = compareData.simulation_results.selected_edge_metrics;
                    
                    // 更新行人路段对比图表
                    createComparisonEdgePedestrianChart(
                        currentEdgeMetrics.pedestrian_metrics || {},
                        compareEdgeMetrics.pedestrian_metrics || {},
                        compareData.simulation_id
                    );
                    
                    // 更新车辆路段对比图表
                    createComparisonEdgeVehicleChart(
                        currentEdgeMetrics.vehicle_metrics || {},
                        compareEdgeMetrics.vehicle_metrics || {},
                        compareData.simulation_id
                    );
                }
            }

            // 在创建对比图表函数中添加对路段图表的处理
            function createComparisonCharts(currentData, compareData) {
                console.log("创建对比图表，当前方案ID:", currentData.simulation_id, "对比方案ID:", compareData.simulation_id);

                // 兼容新/旧格式，提取四类指标
                function extractMetrics(data) {
                    if (data && data.simulation_results) {
                        if (data.simulation_results.network_metrics) {
                            // 新格式
                            return JSON.parse(JSON.stringify(data.simulation_results.network_metrics));
                        } else {
                            // 旧格式
                            return JSON.parse(JSON.stringify({
                                pedestrian_metrics: data.simulation_results.pedestrian_metrics || {},
                                vehicle_metrics: data.simulation_results.vehicle_metrics || {},
                                vip_vehicle_metrics: data.simulation_results.vip_vehicle_metrics || {},
                                venue_area_metrics: data.simulation_results.venue_area_metrics || {}
                            }));
                        }
                    }
                    // 如果数据格式异常，返回空对象
                    return {
                        vehicle_metrics: {},
                        pedestrian_metrics: {},
                        vip_vehicle_metrics: {},
                        venue_area_metrics: {}
                    };
                }

                const currentMetrics = extractMetrics(currentData);
                const compareMetrics = extractMetrics(compareData);

                console.log("对比图表使用的当前方案数据:", JSON.stringify(currentMetrics.vehicle_metrics));
                console.log("对比图表使用的比较方案数据:", JSON.stringify(compareMetrics.vehicle_metrics));
                
                // 对比车辆指标
                createComparisonVehicleChart(
                    currentMetrics.vehicle_metrics,
                    compareMetrics.vehicle_metrics,
                    compareData.simulation_id
                );

                // 对比行人指标
                createComparisonPedestrianChart(
                    currentMetrics.pedestrian_metrics,
                    compareMetrics.pedestrian_metrics,
                    compareData.simulation_id
                );

                // 对比贵宾车辆指标
                createComparisonVIPChart(
                    currentMetrics.vip_vehicle_metrics,
                    compareMetrics.vip_vehicle_metrics,
                    compareData.simulation_id
                );

                // 对比场馆区域指标
                createComparisonVenueChart(
                    currentMetrics.venue_area_metrics,
                    compareMetrics.venue_area_metrics,
                    compareData.simulation_id
                );
                
                // 添加对比自选路段指标
                createComparisonEdgeCharts(currentData, compareData);
            }

            // 确保在DOM完全加载后初始化图表
            document.addEventListener('DOMContentLoaded', function() {
                // 这个函数在DOMContentLoaded事件内部，确保DOM元素已加载
                console.log("DOM加载完成，准备初始化图表");

                // 如果图表容器存在但没有数据，可以先用默认数据初始化
                if (document.getElementById('travelTimeChart') &&
                    document.getElementById('waitingTimeChart') &&
                    document.getElementById('waitingCountChart') &&
                    document.getElementById('timeLossDelayChart')) {

                    // 检查是否已经有数据加载
                    if (!window.resultDataLoaded) {
                        console.log("初始化图表...");
                        // 使用默认数据初始化图表
                        const defaultMetrics = {
                            pedestrian_metrics: {
                                average_travel_time: 0,
                                average_waiting_time: 0,
                                average_waiting_count: 0,
                                average_time_loss: 0
                            },
                            vehicle_metrics: {
                                average_travel_time: 0,
                                average_waiting_time: 0,
                                average_waiting_count: 0,
                                average_time_loss: 0
                            },
                            vip_vehicle_metrics: {
                                average_travel_time: 0,
                                average_waiting_time: 0,
                                average_waiting_count: 0,
                                average_time_loss: 0
                            },
                            venue_area_metrics: {
                                average_pedestrian_travel_time: 0,
                                average_pedestrian_delay: 0,
                                average_vehicle_travel_time: 0,
                                average_vehicle_delay: 0
                            }
                        };

                        createTravelTimeChart(defaultMetrics);
                        createWaitingTimeChart(defaultMetrics);
                        createWaitingCountChart(defaultMetrics);
                        createTimeLossDelayChart(defaultMetrics);
                    }
                }
            });

            // 函数：用于从URL参数或JSON文件加载数据
            async function loadResultData() {
                try {
                    // 尝试从URL参数获取JSON
                    const urlParams = new URLSearchParams(window.location.search);
                    const jsonData = urlParams.get('data');

                    if (jsonData) {
                        const parsedData = JSON.parse(decodeURIComponent(jsonData));
                        // 保存到全局变量以供后续使用
                        currentLoadedResultData = parsedData;
                        return parsedData;
                    }

                    // 尝试从文件加载JSON（实际应用可能需要更复杂的逻辑）
                    const urlParams2 = new URLSearchParams(window.location.search);
                    const fileParam = urlParams2.get('file');

                    if (fileParam) {
                        const response = await fetch(fileParam);
                        if (response.ok) {
                            const loadedData = await response.json();
                            // 保存到全局变量以供后续使用
                            currentLoadedResultData = loadedData;
                            return loadedData;
                        }
                    }

                    // 如果没有获取到数据，返回默认数据
                    const defaultData = getDefaultResultData();
                    // 保存默认数据到全局变量
                    currentLoadedResultData = defaultData;
                    return defaultData;
                } catch (error) {
                    console.error("加载数据失败:", error);
                    const defaultData = getDefaultResultData();
                    // 保存默认数据到全局变量
                    currentLoadedResultData = defaultData;
                    return defaultData; // 出错时使用默认数据
                }
            }

            // 获取默认数据
            function getDefaultResultData() {
                return {
                    simulation_id: "250702174456",
                    start_time: new Date().toISOString(),
                    config_summary: {
                        network: "预设路网 - 仅开放东侧出入口 + 道路限行(2个路段)",
                        signal: "预设配时 + 自定义优化(2个交叉口)",
                        traffic: "预设需求 - 进场场景 + 中规模 + 贵宾专车"
                    },
                    simulation_results: {
                        pedestrian_metrics: {
                            average_travel_time: 123.45,
                            average_waiting_time: 12.34,
                            average_waiting_count: 2.1,
                            average_time_loss: 23.45
                        },
                        vehicle_metrics: {
                            average_travel_time: 234.56,
                            average_waiting_time: 45.67,
                            average_waiting_count: 3.2,
                            average_time_loss: 67.89
                        },
                        vip_vehicle_metrics: {
                            average_travel_time: 145.23,
                            average_waiting_time: 8.91,
                            average_waiting_count: 1.2,
                            average_time_loss: 12.34
                        },
                        venue_area_metrics: {
                            average_pedestrian_travel_time: 156.78,
                            average_pedestrian_delay: 34.56,
                            average_vehicle_travel_time: 267.89,
                            average_vehicle_delay: 78.90
                        }
                    }
                };
            }

            // 工具：补全各类型指标的默认值，防止 NaN/undefined 问题
function ensureAllMetricsDefaults(allMetrics) {
    // 行人
    allMetrics.pedestrian_metrics = {
        average_travel_time: 0,
        average_waiting_time: 0,
        average_waiting_count: 0,
        average_time_loss: 0,
        ...allMetrics.pedestrian_metrics
    };
    // 一般车辆
    allMetrics.vehicle_metrics = {
        average_travel_time: 0,
        average_waiting_time: 0,
        average_waiting_count: 0,
        average_time_loss: 0,
        ...allMetrics.vehicle_metrics
    };
    // VIP车
    allMetrics.vip_vehicle_metrics = {
        average_travel_time: 0,
        average_waiting_time: 0,
        average_waiting_count: 0,
        average_time_loss: 0,
        ...allMetrics.vip_vehicle_metrics
    };
    // 场馆
    allMetrics.venue_area_metrics = {
        average_vehicle_travel_time: 0,
        average_vehicle_delay: 0,
        average_pedestrian_travel_time: 0,
        average_pedestrian_delay: 0,
        ...allMetrics.venue_area_metrics
    };
}




            // 安全创建图表
            function safeCreateChart(chartFunctionName, metrics) {
                try {
                    // 确保指标有默认值
                    const safeMetrics = ensureMetricsDefaults(metrics || {});

                    // 检查图表创建函数是否存在
                    if (typeof window[chartFunctionName] === 'function') {
                        window[chartFunctionName](safeMetrics);
                    } else {
                        console.warn(`图表创建函数 ${chartFunctionName} 不存在`);
                    }
                } catch (error) {
                    console.error(`创建图表时出错 (${chartFunctionName}):`, error);
                }
            }

            // 显示路段分析结果
            function displayEdgeAnalysisResults(edgeMetrics) {
                console.log("显示路段分析结果", edgeMetrics);
                const edgeSection = document.getElementById('edgeAnalysisSection');
                if (edgeSection) {
                    // 确保分析区域可见
                    edgeSection.style.display = 'block';

                    // 安全更新路段指标的辅助函数
                    function safeUpdateEdgeMetric(elementId, value, defaultValue = '-', isPercentage = false, decimals = 2) {
                        const element = document.getElementById(elementId);
                        if (element) {
                            if (value !== undefined && value !== null && !isNaN(value)) {
                                if (isPercentage) {
                                    element.textContent = (Number(value) * 100).toFixed(1) + '%';
                                } else {
                                    element.textContent = Number(value).toFixed(decimals);
                                }
                            } else {
                                element.textContent = defaultValue;
                            }
                        }
                    }

                    // 更新路段行人指标
                    const edgePedMetrics = edgeMetrics.pedestrian_metrics || {};
                    console.log("行人路段指标数据:", edgePedMetrics);
                    safeUpdateEdgeMetric('edgePedTravelTime', edgePedMetrics.avg_traveltime);
                    safeUpdateEdgeMetric('edgePedWaitingTime', edgePedMetrics.avg_waitingTime);
                    safeUpdateEdgeMetric('edgePedSpeed', edgePedMetrics.avg_speed);
                    safeUpdateEdgeMetric('edgePedTimeLoss', edgePedMetrics.avg_timeLoss);
                    safeUpdateEdgeMetric('edgePedCount', edgePedMetrics.total_entered, '-', false, 0);
                    safeUpdateEdgeMetric('edgePedOccupancy', edgePedMetrics.avg_occupancy, '-', true);

                    // 更新路段车辆指标
                    const edgeVehMetrics = edgeMetrics.vehicle_metrics || {};
                    console.log("车辆路段指标数据:", edgeVehMetrics);
                    safeUpdateEdgeMetric('edgeVehTravelTime', edgeVehMetrics.avg_traveltime);
                    safeUpdateEdgeMetric('edgeVehWaitingTime', edgeVehMetrics.avg_waitingTime);
                    safeUpdateEdgeMetric('edgeVehSpeed', edgeVehMetrics.avg_speed);
                    safeUpdateEdgeMetric('edgeVehTimeLoss', edgeVehMetrics.avg_timeLoss);
                    safeUpdateEdgeMetric('edgeVehCount', edgeVehMetrics.total_entered, '-', false, 0);
                    safeUpdateEdgeMetric('edgeVehOccupancy', edgeVehMetrics.avg_occupancy, '-', true);

                    // 确保图表容器是可见的
                    const pedChartContainer = document.getElementById('edgePedChart').parentElement;
                    const vehChartContainer = document.getElementById('edgeVehChart').parentElement;
                    
                    console.log("行人图表容器:", pedChartContainer);
                    console.log("车辆图表容器:", vehChartContainer);

                    // 延迟一点时间创建图表，确保DOM已完全渲染
                    setTimeout(() => {
                        try {
                            console.log("创建路段行人图表...");
                            createEdgePedestrianChart(edgePedMetrics);
                            console.log("创建路段车辆图表...");
                            createEdgeVehicleChart(edgeVehMetrics);
                        } catch (error) {
                            console.error("创建路段图表时出错:", error);
                        }
                    }, 100);
                } else {
                    console.warn('未找到路段分析结果显示区域');
                }
            }



            // 修正数据确保有默认值
            function ensureMetricsDefaults(metrics) {
                const defaults = {
                    average_travel_time: 0,
                    average_waiting_time: 0,
                    average_waiting_count: 0,
                    average_time_loss: 0,
                    average_pedestrian_travel_time: 0,
                    average_pedestrian_delay: 0,
                    average_vehicle_travel_time: 0,
                    average_vehicle_delay: 0
                };

                return { ...defaults, ...metrics };
            }

            // // 创建行人指标柱状图
            // function createPedestrianChart(metrics) {
            //     metrics = ensureMetricsDefaults(metrics);
            //     const ctx = document.getElementById('pedChart').getContext('2d');

            //     console.log("创建行人图表，容器:", document.getElementById('pedChart'));

            //     // 确保图表容器可见且有尺寸
            //     const chartContainer = document.getElementById('pedChart').parentElement;
            //     if (chartContainer) {
            //         // 确保图表容器有明确的高度
            //         if (getComputedStyle(chartContainer).height === '0px') {
            //             chartContainer.style.height = '200px';
            //         }
            //     }

            //     // 销毁已存在的图表实例，避免重复创建
            //     if (window.pedestrianChart instanceof Chart) {
            //         window.pedestrianChart.destroy();
            //     }

            //     window.pedestrianChart = new Chart(ctx, {
            //         type: 'bar',
            //         data: {
            //             labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
            //             datasets: [{
            //                 label: '行人指标',
            //                 data: [
            //                     metrics.average_travel_time,
            //                     metrics.average_waiting_time,
            //                     metrics.average_waiting_count,
            //                     metrics.average_time_loss
            //                 ],
            //                 backgroundColor: [
            //                     'rgba(255, 99, 132, 0.8)',
            //                     'rgba(54, 162, 235, 0.8)',
            //                     'rgba(255, 206, 86, 0.8)',
            //                     'rgba(75, 192, 192, 0.8)'
            //                 ],
            //                 borderColor: [
            //                     'rgba(255, 99, 132, 1)',
            //                     'rgba(54, 162, 235, 1)',
            //                     'rgba(255, 206, 86, 1)',
            //                     'rgba(75, 192, 192, 1)'
            //                 ],
            //                 borderWidth: 1
            //             }]
            //         },
            //         options: {
            //             responsive: true,
            //             maintainAspectRatio: false,
            //             scales: {
            //                 y: {
            //                     beginAtZero: true,
            //                     grid: {
            //                         color: 'rgba(255, 255, 255, 0.1)'
            //                     },
            //                     ticks: {
            //                         color: '#ffffff' // 设置y轴刻度文字颜色为白色
            //                     }
            //                 },
            //                 x: {
            //                     grid: {
            //                         color: 'rgba(255, 255, 255, 0.1)'
            //                     },
            //                     ticks: {
            //                         color: '#ffffff' // 设置x轴刻度文字颜色为白色
            //                     }
            //                 }
            //             },
            //             plugins: {
            //                 legend: {
            //                     display: true,
            //                     labels: {
            //                         color: '#ffffff' // 设置图例文字颜色
            //                     }
            //                 },
            //                 title: {
            //                     display: true,
            //                     text: '行人指标',
            //                     color: '#ffffff' // 设置标题文字颜色为白色
            //                 }
            //             }
            //         }
            //     });

            //     console.log("行人图表创建完成", window.pedestrianChart);
            // }

            // // 创建一般车辆指标柱状图
            // function createVehicleChart(metrics) {
            //     metrics = ensureMetricsDefaults(metrics);
            //     const ctx = document.getElementById('vehChart').getContext('2d');

            //     console.log("创建车辆图表，容器:", document.getElementById('vehChart'));
            //     console.log("指标数据:", metrics);

            //     // 确保图表容器可见且有尺寸
            //     const chartContainer = document.getElementById('vehChart').parentElement;
            //     if (chartContainer) {
            //         // 确保图表容器有明确的高度
            //         if (getComputedStyle(chartContainer).height === '0px') {
            //             chartContainer.style.height = '200px';
            //         }
            //     }

            //     // 销毁已存在的图表实例，避免重复创建
            //     if (window.vehicleChart instanceof Chart) {
            //         window.vehicleChart.destroy();
            //     }

            //     window.vehicleChart = new Chart(ctx, {
            //         type: 'bar',
            //         data: {
            //             labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
            //             datasets: [{
            //                 label: '一般车辆指标',
            //                 data: [
            //                     metrics.average_travel_time,
            //                     metrics.average_waiting_time,
            //                     metrics.average_waiting_count,
            //                     metrics.average_time_loss
            //                 ],
            //                 backgroundColor: [
            //                     'rgba(255, 99, 132, 0.8)',
            //                     'rgba(54, 162, 235, 0.8)',
            //                     'rgba(255, 206, 86, 0.8)',
            //                     'rgba(75, 192, 192, 0.8)'
            //                 ],
            //                 borderColor: [
            //                     'rgba(255, 99, 132, 1)',
            //                     'rgba(54, 162, 235, 1)',
            //                     'rgba(255, 206, 86, 1)',
            //                     'rgba(75, 192, 192, 1)'
            //                 ],
            //                 borderWidth: 1
            //             }]
            //         },
            //         options: {
            //             responsive: true,
            //             maintainAspectRatio: false,
            //             scales: {
            //                 y: {
            //                     beginAtZero: true,
            //                     grid: {
            //                         color: 'rgba(255, 255, 255, 0.1)'
            //                     },
            //                     ticks: {
            //                         color: '#ffffff' // 设置y轴刻度文字颜色为白色
            //                     }
            //                 },
            //                 x: {
            //                     grid: {
            //                         color: 'rgba(255, 255, 255, 0.1)'
            //                     },
            //                     ticks: {
            //                         color: '#ffffff' // 设置x轴刻度文字颜色为白色
            //                     }
            //                 }
            //             },
            //             plugins: {
            //                 legend: {
            //                     display: true,
            //                     labels: {
            //                         color: '#ffffff' // 设置图例文字颜色
            //                     }
            //                 },
            //                 title: {
            //                     display: true,
            //                     text: '一般车辆指标',
            //                     color: '#ffffff' // 设置标题文字颜色为白色
            //                 }
            //             }
            //         }
            //     });

            //     console.log("车辆图表创建完成", window.vehicleChart);
            // }

            // // 创建贵宾专车指标柱状图
            // function createVIPVehicleChart(metrics) {
            //     metrics = ensureMetricsDefaults(metrics);
            //     const ctx = document.getElementById('vipChart').getContext('2d');

            //     console.log("创建贵宾专车图表，容器:", document.getElementById('vipChart'));

            //     // 确保图表容器可见且有尺寸
            //     const chartContainer = document.getElementById('vipChart').parentElement;
            //     if (chartContainer) {
            //         // 确保图表容器有明确的高度
            //         if (getComputedStyle(chartContainer).height === '0px') {
            //             chartContainer.style.height = '200px';
            //         }
            //     }

            //     // 销毁已存在的图表实例，避免重复创建
            //     if (window.vipVehicleChart instanceof Chart) {
            //         window.vipVehicleChart.destroy();
            //     }

            //     window.vipVehicleChart = new Chart(ctx, {
            //         type: 'bar',
            //         data: {
            //             labels: ['平均行程时间', '平均等待时间', '平均等待次数', '平均时间损失'],
            //             datasets: [{
            //                 label: '贵宾专车指标',
            //                 data: [
            //                     metrics.average_travel_time,
            //                     metrics.average_waiting_time,
            //                     metrics.average_waiting_count,
            //                     metrics.average_time_loss
            //                 ],
            //                 backgroundColor: [
            //                     'rgba(255, 99, 132, 0.8)',
            //                     'rgba(54, 162, 235, 0.8)',
            //                     'rgba(255, 206, 86, 0.8)',
            //                     'rgba(75, 192, 192, 0.8)'
            //                 ],
            //                 borderColor: [
            //                     'rgba(255, 99, 132, 1)',
            //                     'rgba(54, 162, 235, 1)',
            //                     'rgba(255, 206, 86, 1)',
            //                     'rgba(75, 192, 192, 1)'
            //                 ],
            //                 borderWidth: 1
            //             }]
            //         },
            //         options: {
            //             responsive: true,
            //             maintainAspectRatio: false,
            //             scales: {
            //                 y: {
            //                     beginAtZero: true,
            //                     grid: {
            //                         color: 'rgba(255, 255, 255, 0.1)'
            //                     },
            //                     ticks: {
            //                         color: '#ffffff' // 设置y轴刻度文字颜色为白色
            //                     }
            //                 },
            //                 x: {
            //                     grid: {
            //                         color: 'rgba(255, 255, 255, 0.1)'
            //                     },
            //                     ticks: {
            //                         color: '#ffffff' // 设置x轴刻度文字颜色为白色
            //                     }
            //                 }
            //             },
            //             plugins: {
            //                 legend: {
            //                     display: true,
            //                     labels: {
            //                         color: '#ffffff' // 设置图例文字颜色
            //                     }
            //                 },
            //                 title: {
            //                     display: true,
            //                     text: '贵宾专车指标',
            //                     color: '#ffffff' // 设置标题文字颜色为白色
            //                 }
            //             }
            //         }
            //     });

            //     console.log("贵宾专车图表创建完成", window.vipVehicleChart);
            // }

            // // 创建场馆区域指标柱状图
            // function createVenueChart(metrics) {
            //     metrics = ensureMetricsDefaults({
            //         average_pedestrian_travel_time: metrics.average_pedestrian_travel_time,
            //         average_pedestrian_delay: metrics.average_pedestrian_delay,
            //         average_vehicle_travel_time: metrics.average_vehicle_travel_time,
            //         average_vehicle_delay: metrics.average_vehicle_delay
            //     });
            //     const ctx = document.getElementById('venueChart').getContext('2d');

            //     console.log("创建场馆区域图表，容器:", document.getElementById('venueChart'));

            //     // 确保图表容器可见且有尺寸
            //     const chartContainer = document.getElementById('venueChart').parentElement;
            //     if (chartContainer) {
            //         // 确保图表容器有明确的高度
            //         if (getComputedStyle(chartContainer).height === '0px') {
            //             chartContainer.style.height = '200px';
            //         }
            //     }

            //     // 销毁已存在的图表实例，避免重复创建
            //     if (window.venueChart instanceof Chart) {
            //         window.venueChart.destroy();
            //     }

            //     window.venueChart = new Chart(ctx, {
            //         type: 'bar',
            //         data: {
            //             labels: ['行人平均行程时间', '行人平均延误', '车辆平均行程时间', '车辆平均延误'],
            //             datasets: [{
            //                 label: '场馆区域指标',
            //                 data: [
            //                     metrics.average_pedestrian_travel_time,
            //                     metrics.average_pedestrian_delay,
            //                     metrics.average_vehicle_travel_time,
            //                     metrics.average_vehicle_delay
            //                 ],
            //                 backgroundColor: [
            //                     'rgba(255, 99, 132, 0.8)',
            //                     'rgba(54, 162, 235, 0.8)',
            //                     'rgba(255, 206, 86, 0.8)',
            //                     'rgba(75, 192, 192, 0.8)'
            //                 ],
            //                 borderColor: [
            //                     'rgba(255, 99, 132, 1)',
            //                     'rgba(54, 162, 235, 1)',
            //                     'rgba(255, 206, 86, 1)',
            //                     'rgba(75, 192, 192, 1)'
            //                 ],
            //                 borderWidth: 1
            //             }]
            //         },
            //         options: {
            //             responsive: true,
            //             maintainAspectRatio: false,
            //             scales: {
            //                 y: {
            //                     beginAtZero: true,
            //                     grid: { color: 'rgba(255, 255, 255, 0.1)' },
            //                     ticks: { color: '#ffffff' }
            //                 },
            //                 x: {
            //                     grid: { color: 'rgba(255, 255, 255, 0.1)' },
            //                     ticks: { color: '#ffffff' }
            //                 }
            //             },
            //             plugins: {
            //                 legend: {
            //                     display: true,
            //                     labels: { color: '#ffffff' }
            //                 },
            //                 title: {
            //                     display: true,
            //                     text: '场馆区域指标',
            //                     color: '#ffffff' // 设置标题文字颜色为白色
            //                 }
            //             }
            //         }
            //     });

            //     console.log("场馆区域图表创建完成", window.venueChart);
            // }

            // 创建路段行人指标柱状图
            function createEdgePedestrianChart(metrics) {
                console.log("开始创建行人路段图表，数据:", metrics);
                try {
                    // 确保metrics有默认值
                    const defaultMetrics = {
                        avg_traveltime: 0,
                        avg_waitingTime: 0,
                        avg_speed: 0,
                        avg_timeLoss: 0,
                        total_entered: 0,
                        avg_occupancy: 0
                    };
                    metrics = { ...defaultMetrics, ...metrics };
                    
                    const canvas = document.getElementById('edgePedChart');
                    if (!canvas) {
                        console.error("找不到行人路段图表canvas元素");
                        return;
                    }
                    
                    console.log("获取图表上下文...");
                    const ctx = canvas.getContext('2d');
                    if (!ctx) {
                        console.error("无法获取行人路段图表的2d上下文");
                        return;
                    }
                    
                    // 确保图表容器可见且有尺寸
                    const chartContainer = canvas.parentElement;
                    if (chartContainer) {
                        console.log("图表容器尺寸:", chartContainer.offsetWidth, "x", chartContainer.offsetHeight);
                        // 确保图表容器有明确的高度
                        if (chartContainer.offsetHeight < 100) {
                            console.log("调整图表容器高度");
                            chartContainer.style.height = '300px';
                            chartContainer.style.minHeight = '200px';
                        }
                    }
                    
                    // 销毁已存在的图表实例
                    if (window.edgePedChart instanceof Chart) {
                        console.log("销毁旧的行人路段图表实例");
                        window.edgePedChart.destroy();
                    }
                    
                    console.log("创建新的行人路段图表...");
                    window.edgePedChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['平均行程时间', '平均等待时间', '平均时间损失', '平均速度'],
                            datasets: [{
                                label: '行人路段指标',
                                data: [
                                    metrics.avg_traveltime || 0,
                                    metrics.avg_waitingTime || 0,
                                    metrics.avg_timeLoss || 0,
                                    metrics.avg_speed || 0
                                ],
                                backgroundColor: [
                                    'rgba(255, 99, 132, 0.8)',
                                    'rgba(54, 162, 235, 0.8)',
                                    'rgba(255, 206, 86, 0.8)',
                                    'rgba(75, 192, 192, 0.8)'
                                ],
                                borderColor: [
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)',
                                    'rgba(75, 192, 192, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                    ticks: {
                                        color: '#ffffff',
                                        font: { size: 28 } /* 调大Y轴刻度字体 */
                                    }
                                },
                                x: {
                                    grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                    ticks: {
                                        color: '#ffffff',
                                        font: { size: 28 } /* 调大X轴刻度字体 */
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    display: true,
                                    labels: {
                                        color: '#ffffff',
                                        font: { size: 14 } /* 调大图例字体 */
                                    }
                                },
                                title: {
                                    display: true,
                                    text: '行人路段指标',
                                    color: '#ffffff',
                                    font: { size: 16 } /* 调大标题字体 */
                                },
                                tooltip: {
                                    titleFont: { size: 24 }, /* 调大tooltip标题字体 */
                                    bodyFont: { size: 22 }   /* 调大tooltip内容字体 */
                                }
                            }
                        }
                    });
                    console.log("行人路段图表创建成功:", window.edgePedChart);
                } catch (error) {
                    console.error("创建行人路段图表时出错:", error);
                }
            }
            
            // 创建路段车辆指标柱状图
            function createEdgeVehicleChart(metrics) {
                console.log("开始创建车辆路段图表，数据:", metrics);
                try {
                    // 确保metrics有默认值
                    const defaultMetrics = {
                        avg_traveltime: 0,
                        avg_waitingTime: 0,
                        avg_speed: 0,
                        avg_timeLoss: 0,
                        total_entered: 0,
                        avg_occupancy: 0
                    };
                    metrics = { ...defaultMetrics, ...metrics };
                    
                    const canvas = document.getElementById('edgeVehChart');
                    if (!canvas) {
                        console.error("找不到车辆路段图表canvas元素");
                        return;
                    }
                    
                    console.log("获取图表上下文...");
                    const ctx = canvas.getContext('2d');
                    if (!ctx) {
                        console.error("无法获取车辆路段图表的2d上下文");
                        return;
                    }
                    
                    // 确保图表容器可见且有尺寸
                    const chartContainer = canvas.parentElement;
                    if (chartContainer) {
                        console.log("图表容器尺寸:", chartContainer.offsetWidth, "x", chartContainer.offsetHeight);
                        // 确保图表容器有明确的高度
                        if (chartContainer.offsetHeight < 100) {
                            console.log("调整图表容器高度");
                            chartContainer.style.height = '300px';
                            chartContainer.style.minHeight = '200px';
                        }
                    }
                    
                    // 销毁已存在的图表实例
                    if (window.edgeVehChart instanceof Chart) {
                        console.log("销毁旧的车辆路段图表实例");
                        window.edgeVehChart.destroy();
                    }
                    
                    console.log("创建新的车辆路段图表...");
                    window.edgeVehChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['平均行程时间', '平均等待时间', '平均时间损失', '平均速度'],
                            datasets: [{
                                label: '车辆路段指标',
                                data: [
                                    metrics.avg_traveltime || 0,
                                    metrics.avg_waitingTime || 0,
                                    metrics.avg_timeLoss || 0,
                                    metrics.avg_speed || 0
                                ],
                                backgroundColor: [
                                    'rgba(255, 99, 132, 0.8)',
                                    'rgba(54, 162, 235, 0.8)',
                                    'rgba(255, 206, 86, 0.8)',
                                    'rgba(75, 192, 192, 0.8)'
                                ],
                                borderColor: [
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)',
                                    'rgba(75, 192, 192, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                    ticks: {
                                        color: '#ffffff',
                                        font: { size: 14 } /* 调大Y轴刻度字体 */
                                    }
                                },
                                x: {
                                    grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                    ticks: {
                                        color: '#ffffff',
                                        font: { size: 14 } /* 调大X轴刻度字体 */
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    display: true,
                                    labels: {
                                        color: '#ffffff',
                                        font: { size: 14 } /* 调大图例字体 */
                                    }
                                },
                                title: {
                                    display: true,
                                    text: '车辆路段指标',
                                    color: '#ffffff',
                                    font: { size: 16 } /* 调大标题字体 */
                                },
                                tooltip: {
                                    titleFont: { size: 24 }, /* 调大tooltip标题字体 */
                                    bodyFont: { size: 22 }   /* 调大tooltip内容字体 */
                                }
                            }
                        }
                    });
                    console.log("车辆路段图表创建成功:", window.edgeVehChart);
                } catch (error) {
                    console.error("创建车辆路段图表时出错:", error);
                }
            }
            
            // 创建路段行人指标对比图
            function createComparisonEdgePedestrianChart(currentMetrics, compareMetrics, compareId) {
                const ctx = document.getElementById('edgePedChart').getContext('2d');
                
                // 销毁已存在的图表实例
                if (window.edgePedChart instanceof Chart) {
                    window.edgePedChart.destroy();
                }
                
                window.edgePedChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['平均行程时间', '平均等待时间', '平均时间损失', '平均速度'],
                        datasets: [
                            {
                                label: '当前方案',
                                data: [
                                    currentMetrics.avg_traveltime,
                                    currentMetrics.avg_waitingTime,
                                    currentMetrics.avg_timeLoss,
                                    currentMetrics.avg_speed
                                ],
                                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            },
                            {
                                label: `对比方案 (${compareId})`,
                                data: [
                                    compareMetrics.avg_traveltime,
                                    compareMetrics.avg_waitingTime,
                                    compareMetrics.avg_timeLoss,
                                    compareMetrics.avg_speed
                                ],
                                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: {
                                    color: '#ffffff',
                                    font: { size: 28 } /* 调大Y轴刻度字体 */
                                }
                            },
                            x: {
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: {
                                    color: '#ffffff',
                                    font: { size: 28 } /* 调大X轴刻度字体 */
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                labels: {
                                    color: '#ffffff',
                                    font: { size: 28 } /* 调大图例字体 */
                                }
                            },
                            title: {
                                display: true,
                                text: '行人路段指标对比',
                                color: '#ffffff',
                                font: { size: 30 } /* 调大标题字体 */
                            },
                            tooltip: {
                                titleFont: { size: 24 }, /* 调大tooltip标题字体 */
                                bodyFont: { size: 22 }   /* 调大tooltip内容字体 */
                            }
                        }
                    }
                });
            }
            
            // 创建路段车辆指标对比图
            function createComparisonEdgeVehicleChart(currentMetrics, compareMetrics, compareId) {
                const ctx = document.getElementById('edgeVehChart').getContext('2d');
                
                // 销毁已存在的图表实例
                if (window.edgeVehChart instanceof Chart) {
                    window.edgeVehChart.destroy();
                }
                
                window.edgeVehChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['平均行程时间', '平均等待时间', '平均时间损失', '平均速度'],
                        datasets: [
                            {
                                label: '当前方案',
                                data: [
                                    currentMetrics.avg_traveltime,
                                    currentMetrics.avg_waitingTime,
                                    currentMetrics.avg_timeLoss,
                                    currentMetrics.avg_speed
                                ],
                                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            },
                            {
                                label: `对比方案 (${compareId})`,
                                data: [
                                    compareMetrics.avg_traveltime,
                                    compareMetrics.avg_waitingTime,
                                    compareMetrics.avg_timeLoss,
                                    compareMetrics.avg_speed
                                ],
                                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: {
                                    color: '#ffffff',
                                    font: { size: 28 } /* 调大Y轴刻度字体 */
                                }
                            },
                            x: {
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: {
                                    color: '#ffffff',
                                    font: { size: 28 } /* 调大X轴刻度字体 */
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                labels: {
                                    color: '#ffffff',
                                    font: { size: 28 } /* 调大图例字体 */
                                }
                            },
                            title: {
                                display: true,
                                text: '车辆路段指标对比',
                                color: '#ffffff',
                                font: { size: 30 } /* 调大标题字体 */
                            },
                            tooltip: {
                                titleFont: { size: 24 }, /* 调大tooltip标题字体 */
                                bodyFont: { size: 22 }   /* 调大tooltip内容字体 */
                            }
                        }
                    }
                });
            }

            // 在文档加载完成后添加额外的检查，确保路段分析区域的显示状态
            document.addEventListener('DOMContentLoaded', function() {
                // ... existing code ...

                // 添加路段分析相关的初始化代码
                const edgeAnalysisCheckbox = document.getElementById('edgeAnalysis');
                if (edgeAnalysisCheckbox) {
                    edgeAnalysisCheckbox.addEventListener('change', function() {
                        const edgeAnalysisDetails = document.getElementById('edgeAnalysisDetails');
                        if (edgeAnalysisDetails) {
                            edgeAnalysisDetails.style.display = this.checked ? 'block' : 'none';
                        }
                        
                        // 如果取消勾选，隐藏分析结果区域
                        const edgeAnalysisSection = document.getElementById('edgeAnalysisSection');
                        if (!this.checked && edgeAnalysisSection) {
                            edgeAnalysisSection.style.display = 'none';
                        }
                    });
                    
                    // 初始化显示状态
                    const edgeAnalysisDetails = document.getElementById('edgeAnalysisDetails');
                    if (edgeAnalysisDetails) {
                        edgeAnalysisDetails.style.display = edgeAnalysisCheckbox.checked ? 'block' : 'none';
                    }
                }
                
                // 如果有selected_edge_metrics数据，确保显示路段分析结果
                if (window.currentLoadedResultData && 
                    window.currentLoadedResultData.simulation_results && 
                    window.currentLoadedResultData.simulation_results.selected_edge_metrics) {
                    
                    console.log("检测到路段分析数据，显示结果区域");
                    const edgeAnalysisSection = document.getElementById('edgeAnalysisSection');
                    if (edgeAnalysisSection) {
                        edgeAnalysisSection.style.display = 'block';
                    }
                    
                    // 强制更新路段分析图表
                    setTimeout(() => {
                        displayEdgeAnalysisResults(window.currentLoadedResultData.simulation_results.selected_edge_metrics);
                    }, 500);
                }
            });
        });
    </script>
    <script src="network_selector.js"></script>
    <script>
        // 创建平均行程时间柱状图（合并所有类型）
        function createTravelTimeChart(metrics) {
            const ctx = document.getElementById('travelTimeChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.travelTimeChart instanceof Chart) {
                window.travelTimeChart.destroy();
            }
            
            window.travelTimeChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人', '一般车辆', '贵宾专车', '场馆区域行人', '场馆区域车辆'],
                    datasets: [{
                        label: '平均行程时间 (秒)',
                        data: [
                            metrics.pedestrian_metrics.average_travel_time,
                            metrics.vehicle_metrics.average_travel_time,
                            metrics.vip_vehicle_metrics.average_travel_time,
                            metrics.venue_area_metrics.average_pedestrian_travel_time,
                            metrics.venue_area_metrics.average_vehicle_travel_time
                        ],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 206, 86, 0.8)',
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(153, 102, 255, 0.8)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: {
                                color: '#ffffff',
                                font: { size: 28 } /* 调大Y轴刻度字体 */
                            }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: {
                                color: '#ffffff',
                                font: { size: 28 } /* 调大X轴刻度字体 */
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: {
                                color: '#ffffff',
                                font: { size: 28 } /* 调大图例字体 */
                            }
                        },
                        tooltip: {
                            titleFont: { size: 24 }, /* 调大tooltip标题字体 */
                            bodyFont: { size: 22 }   /* 调大tooltip内容字体 */
                        }
                    }
                }
            });
        }

        // 创建平均等待时间柱状图（合并行人、车辆、贵宾车）
        function createWaitingTimeChart(metrics) {
            const ctx = document.getElementById('waitingTimeChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.waitingTimeChart instanceof Chart) {
                window.waitingTimeChart.destroy();
            }
            
            window.waitingTimeChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人', '一般车辆', '贵宾专车'],
                    datasets: [{
                        label: '平均等待时间 (秒)',
                        data: [
                            metrics.pedestrian_metrics.average_waiting_time,
                            metrics.vehicle_metrics.average_waiting_time,
                            metrics.vip_vehicle_metrics.average_waiting_time
                        ],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 206, 86, 0.8)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: {
                                color: '#ffffff',
                                font: { size: 28 } /* 调大Y轴刻度字体 */
                            }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: {
                                color: '#ffffff',
                                font: { size: 28 } /* 调大X轴刻度字体 */
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: {
                                color: '#ffffff',
                                font: { size: 28 } /* 调大图例字体 */
                            }
                        },
                        tooltip: {
                            titleFont: { size: 24 }, /* 调大tooltip标题字体 */
                            bodyFont: { size: 22 }   /* 调大tooltip内容字体 */
                        }
                    }
                }
            });
        }

        // 创建平均等待次数柱状图
        function createWaitingCountChart(metrics) {
            const ctx = document.getElementById('waitingCountChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.waitingCountChart instanceof Chart) {
                window.waitingCountChart.destroy();
            }
            
            window.waitingCountChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人', '一般车辆', '贵宾专车'],
                    datasets: [{
                        label: '平均等待次数',
                        data: [
                            metrics.pedestrian_metrics.average_waiting_count,
                            metrics.vehicle_metrics.average_waiting_count,
                            metrics.vip_vehicle_metrics.average_waiting_count
                        ],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 206, 86, 0.8)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: {
                                color: '#ffffff',
                                font: { size: 28 } /* 调大Y轴刻度字体 */
                            }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: {
                                color: '#ffffff',
                                font: { size: 28 } /* 调大X轴刻度字体 */
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: {
                                color: '#ffffff',
                                font: { size: 28 } /* 调大图例字体 */
                            }
                        },
                        tooltip: {
                            titleFont: { size: 24 }, /* 调大tooltip标题字体 */
                            bodyFont: { size: 22 }   /* 调大tooltip内容字体 */
                        }
                    }
                }
            });
        }

        // 创建时间损失和延误柱状图
        function createTimeLossDelayChart(metrics) {
            const ctx = document.getElementById('timeLossDelayChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.timeLossDelayChart instanceof Chart) {
                window.timeLossDelayChart.destroy();
            }
            
            window.timeLossDelayChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人时间损失', '一般车辆时间损失', '贵宾专车时间损失', '场馆区域行人延误', '场馆区域车辆延误'],
                    datasets: [{
                        label: '时间损失/延误 (秒)',
                        data: [
                            metrics.pedestrian_metrics.average_time_loss,
                            metrics.vehicle_metrics.average_time_loss,
                            metrics.vip_vehicle_metrics.average_time_loss,
                            metrics.venue_area_metrics.average_pedestrian_delay,
                            metrics.venue_area_metrics.average_vehicle_delay
                        ],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 206, 86, 0.8)',
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(153, 102, 255, 0.8)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: {
                                color: '#ffffff',
                                font: { size: 28 } /* 调大Y轴刻度字体 */
                            }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: {
                                color: '#ffffff',
                                font: { size: 28 }, /* 调大X轴刻度字体 */
                                maxRotation: 45,
                                minRotation: 45
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: {
                                color: '#ffffff',
                                font: { size: 28 } /* 调大图例字体 */
                            }
                        },
                        tooltip: {
                            titleFont: { size: 24 }, /* 调大tooltip标题字体 */
                            bodyFont: { size: 22 }   /* 调大tooltip内容字体 */
                        }
                    }
                }
            });
        }

        // 创建方案对比时的图表
        // 平均行程时间对比图表
        function createComparisonTravelTimeChart(currentMetrics, compareMetrics, compareId) {
            const ctx = document.getElementById('travelTimeChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.travelTimeChart instanceof Chart) {
                window.travelTimeChart.destroy();
            }
            
            window.travelTimeChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人', '一般车辆', '贵宾专车', '场馆区域行人', '场馆区域车辆'],
                    datasets: [
                        {
                            label: '当前方案',
                            data: [
                                currentMetrics.pedestrian_metrics.average_travel_time,
                                currentMetrics.vehicle_metrics.average_travel_time,
                                currentMetrics.vip_vehicle_metrics.average_travel_time,
                                currentMetrics.venue_area_metrics.average_pedestrian_travel_time,
                                currentMetrics.venue_area_metrics.average_vehicle_travel_time
                            ],
                            backgroundColor: 'rgba(54, 162, 235, 0.8)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        },
                        {
                            label: `对比方案 (${compareId})`,
                            data: [
                                compareMetrics.pedestrian_metrics.average_travel_time,
                                compareMetrics.vehicle_metrics.average_travel_time,
                                compareMetrics.vip_vehicle_metrics.average_travel_time,
                                compareMetrics.venue_area_metrics.average_pedestrian_travel_time,
                                compareMetrics.venue_area_metrics.average_vehicle_travel_time
                            ],
                            backgroundColor: 'rgba(255, 99, 132, 0.8)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: { color: '#ffffff' }
                        }
                    }
                }
            });
        }

        // 平均等待时间对比图表
        function createComparisonWaitingTimeChart(currentMetrics, compareMetrics, compareId) {
            const ctx = document.getElementById('waitingTimeChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.waitingTimeChart instanceof Chart) {
                window.waitingTimeChart.destroy();
            }
            
            window.waitingTimeChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人', '一般车辆', '贵宾专车'],
                    datasets: [
                        {
                            label: '当前方案',
                            data: [
                                currentMetrics.pedestrian_metrics.average_waiting_time,
                                currentMetrics.vehicle_metrics.average_waiting_time,
                                currentMetrics.vip_vehicle_metrics.average_waiting_time
                            ],
                            backgroundColor: 'rgba(54, 162, 235, 0.8)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        },
                        {
                            label: `对比方案 (${compareId})`,
                            data: [
                                compareMetrics.pedestrian_metrics.average_waiting_time,
                                compareMetrics.vehicle_metrics.average_waiting_time,
                                compareMetrics.vip_vehicle_metrics.average_waiting_time
                            ],
                            backgroundColor: 'rgba(255, 99, 132, 0.8)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: { color: '#ffffff' }
                        }
                    }
                }
            });
        }

        // 平均等待次数对比图表
        function createComparisonWaitingCountChart(currentMetrics, compareMetrics, compareId) {
            const ctx = document.getElementById('waitingCountChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.waitingCountChart instanceof Chart) {
                window.waitingCountChart.destroy();
            }
            
            window.waitingCountChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人', '一般车辆', '贵宾专车'],
                    datasets: [
                        {
                            label: '当前方案',
                            data: [
                                currentMetrics.pedestrian_metrics.average_waiting_count,
                                currentMetrics.vehicle_metrics.average_waiting_count,
                                currentMetrics.vip_vehicle_metrics.average_waiting_count
                            ],
                            backgroundColor: 'rgba(54, 162, 235, 0.8)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        },
                        {
                            label: `对比方案 (${compareId})`,
                            data: [
                                compareMetrics.pedestrian_metrics.average_waiting_count,
                                compareMetrics.vehicle_metrics.average_waiting_count,
                                compareMetrics.vip_vehicle_metrics.average_waiting_count
                            ],
                            backgroundColor: 'rgba(255, 99, 132, 0.8)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: { color: '#ffffff' }
                        }
                    }
                }
            });
        }

        // 时间损失与延误对比图表
        function createComparisonTimeLossDelayChart(currentMetrics, compareMetrics, compareId) {
            const ctx = document.getElementById('timeLossDelayChart').getContext('2d');
            
            // 销毁已存在的图表实例
            if (window.timeLossDelayChart instanceof Chart) {
                window.timeLossDelayChart.destroy();
            }
            
            window.timeLossDelayChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['行人时间损失', '一般车辆时间损失', '贵宾专车时间损失', '场馆区域行人延误', '场馆区域车辆延误'],
                    datasets: [
                        {
                            label: '当前方案',
                            data: [
                                currentMetrics.pedestrian_metrics.average_time_loss,
                                currentMetrics.vehicle_metrics.average_time_loss,
                                currentMetrics.vip_vehicle_metrics.average_time_loss,
                                currentMetrics.venue_area_metrics.average_pedestrian_delay,
                                currentMetrics.venue_area_metrics.average_vehicle_delay
                            ],
                            backgroundColor: 'rgba(54, 162, 235, 0.8)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        },
                        {
                            label: `对比方案 (${compareId})`,
                            data: [
                                compareMetrics.pedestrian_metrics.average_time_loss,
                                compareMetrics.vehicle_metrics.average_time_loss,
                                compareMetrics.vip_vehicle_metrics.average_time_loss,
                                compareMetrics.venue_area_metrics.average_pedestrian_delay,
                                compareMetrics.venue_area_metrics.average_vehicle_delay
                            ],
                            backgroundColor: 'rgba(255, 99, 132, 0.8)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' }
                        },
                        x: {
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#ffffff' },
                            maxRotation: 45,
                            minRotation: 45
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            labels: { color: '#ffffff' }
                        }
                    }
                }
            });
        }

        // 确保所有指标都有默认值
        function ensureAllMetricsDefaults(allMetrics) {
            const defaults = {
                average_travel_time: 0,
                average_waiting_time: 0,
                average_waiting_count: 0,
                average_time_loss: 0,
                average_pedestrian_travel_time: 0,
                average_pedestrian_delay: 0,
                average_vehicle_travel_time: 0,
                average_vehicle_delay: 0
            };
            
            // 为每组指标设置默认值
            for (const key in allMetrics) {
                allMetrics[key] = { ...defaults, ...allMetrics[key] };
            }
            
            return allMetrics;
        }
    </script>

    <script>
        function updateZoomDisplay() {
            const zoomDisplay = document.getElementById('zoomDisplay');
            if (zoomDisplay) {
                const zoom = Math.round((window.outerWidth / window.innerWidth) * 100);
                const vw = window.innerWidth;
                const vh = window.innerHeight;
                zoomDisplay.textContent = `当前缩放: ${zoom}% | 视口: ${vw}×${vh}`;
            }
        }

        function showTestOverlay() {
            const overlay = document.getElementById('testOverlay');
            if (overlay) {
                overlay.style.display = 'flex';
                updateZoomDisplay();
            }
        }

        function hideTestOverlay() {
            const overlay = document.getElementById('testOverlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        // 监听窗口变化
        window.addEventListener('resize', updateZoomDisplay);

        // 添加键盘快捷键来显示/隐藏测试覆盖层
        document.addEventListener('keydown', function(e) {
            // Ctrl + Shift + T 显示测试覆盖层
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                const overlay = document.getElementById('testOverlay');
                if (overlay.style.display === 'none' || overlay.style.display === '') {
                    showTestOverlay();
                } else {
                    hideTestOverlay();
                }
            }
        });

        console.log('🎯 vw/vh 视口单位方案已成功应用到 index.html！');
        console.log('📋 测试方法：');
        console.log('1. 按 Ctrl + Shift + T 显示/隐藏测试覆盖层');
        console.log('2. 使用 Ctrl + 滚轮缩放页面');
        console.log('3. 观察所有元素是否保持完美比例');
        console.log('✅ 预期效果：与 final-test.html 完全一致的缩放行为');
    </script>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
</body>
</html>